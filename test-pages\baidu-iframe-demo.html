<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>百度iframe演示页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .iframe-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #007bff;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .iframe-section h2 {
            color: #007bff;
            margin-top: 0;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .main-content {
            margin: 20px 0;
            padding: 20px;
            background-color: #e9ecef;
            border-radius: 8px;
        }
        .test-button {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        .test-button:hover {
            background-color: #218838;
        }
        .test-input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 10px;
            font-size: 16px;
            width: 200px;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ 百度iframe演示页面</h1>
        
        <div class="warning">
            <h3>⚠️ 重要提示：</h3>
            <p>由于同源策略限制，百度网站不允许被嵌入到iframe中。这个页面主要用于演示跨域iframe的限制情况。</p>
            <p>在实际测试中，您会看到iframe无法加载百度内容，这是正常的安全限制。</p>
        </div>

        <div class="info">
            <h3>测试说明：</h3>
            <p>本页面演示了iframe功能的跨域限制：</p>
            <ul>
                <li><strong>同域iframe</strong>: 可以正常访问和操作内部元素</li>
                <li><strong>跨域iframe</strong>: 受同源策略限制，无法访问内部元素</li>
                <li><strong>自动化系统</strong>: 会检测跨域限制并给出相应提示</li>
            </ul>
        </div>

        <div class="main-content">
            <h2>主页面内容</h2>
            <button id="main-button" class="test-button">主页面按钮</button>
            <input id="main-input" class="test-input" placeholder="主页面输入框" />
            <p>这些元素在主页面中，可以正常定位和操作。</p>
        </div>

        <div class="iframe-section">
            <h2>百度搜索iframe（跨域限制演示）</h2>
            <p>iframe选择器: <code>#baidu-iframe</code></p>
            <p>预期结果: 由于跨域限制，iframe无法加载百度内容</p>
            <iframe id="baidu-iframe" src="https://www.baidu.com" title="百度搜索"></iframe>
        </div>

        <div class="iframe-section">
            <h2>本地内容iframe（正常工作）</h2>
            <p>iframe选择器: <code>#local-iframe</code></p>
            <p>预期结果: 可以正常访问和操作iframe内的元素</p>
            <iframe id="local-iframe" src="data:text/html;charset=utf-8,
                <!DOCTYPE html>
                <html>
                <head>
                    <style>
                        body { 
                            font-family: Arial, sans-serif; 
                            padding: 20px; 
                            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                            color: white;
                            text-align: center;
                        }
                        .form-group { 
                            margin: 20px 0; 
                            background: rgba(255,255,255,0.1);
                            padding: 15px;
                            border-radius: 8px;
                        }
                        label { 
                            display: block; 
                            margin-bottom: 8px; 
                            font-weight: bold; 
                        }
                        input, button { 
                            padding: 10px 15px; 
                            border: none; 
                            border-radius: 5px; 
                            margin: 5px;
                            font-size: 14px;
                        }
                        button { 
                            background: #28a745; 
                            color: white; 
                            cursor: pointer; 
                            transition: background 0.3s;
                        }
                        button:hover { 
                            background: #218838; 
                        }
                        input {
                            background: rgba(255,255,255,0.9);
                            color: #333;
                        }
                        .success-msg {
                            background: rgba(40, 167, 69, 0.2);
                            border: 1px solid #28a745;
                            padding: 10px;
                            border-radius: 5px;
                            margin: 10px 0;
                            display: none;
                        }
                    </style>
                </head>
                <body>
                    <h3>🎯 iframe内容测试区域</h3>
                    <p>这个iframe可以正常访问，因为它是同域内容。</p>
                    
                    <div class='form-group'>
                        <label for='test-input'>测试输入框:</label>
                        <input type='text' id='test-input' name='test-input' placeholder='请输入测试内容' />
                    </div>
                    
                    <div class='form-group'>
                        <label for='search-input'>搜索框:</label>
                        <input type='text' id='search-input' name='search-input' placeholder='模拟搜索框' />
                    </div>
                    
                    <div class='form-group'>
                        <button id='submit-btn' type='button' onclick='showSuccess()'>提交按钮</button>
                        <button id='search-btn' type='button' onclick='performSearch()'>搜索按钮</button>
                        <button id='clear-btn' type='button' onclick='clearInputs()'>清空按钮</button>
                    </div>
                    
                    <div class='success-msg' id='success-msg'>
                        ✅ 操作成功！iframe内的元素可以正常交互。
                    </div>
                    
                    <div class='form-group'>
                        <p><strong>可测试的元素：</strong></p>
                        <ul style='text-align: left; display: inline-block;'>
                            <li>ID定位: #test-input, #search-input, #submit-btn</li>
                            <li>CSS定位: input[type='text'], button</li>
                            <li>文本定位: '提交按钮', '搜索按钮', '清空按钮'</li>
                            <li>包含文本: '按钮', '输入'</li>
                        </ul>
                    </div>
                    
                    <script>
                        function showSuccess() {
                            document.getElementById('success-msg').style.display = 'block';
                            setTimeout(() => {
                                document.getElementById('success-msg').style.display = 'none';
                            }, 3000);
                        }
                        
                        function performSearch() {
                            const searchInput = document.getElementById('search-input');
                            if (searchInput.value.trim()) {
                                alert('搜索内容: ' + searchInput.value);
                            } else {
                                alert('请输入搜索内容');
                            }
                        }
                        
                        function clearInputs() {
                            document.getElementById('test-input').value = '';
                            document.getElementById('search-input').value = '';
                            document.getElementById('success-msg').style.display = 'none';
                        }
                    </script>
                </body>
                </html>
            " title="本地内容"></iframe>
        </div>

        <div class="info">
            <h3>测试建议：</h3>
            <ol>
                <li><strong>测试主页面元素</strong>: 使用普通定位策略测试主页面的按钮和输入框</li>
                <li><strong>测试跨域iframe</strong>: 尝试使用iframe-*策略定位百度iframe内容，会收到跨域限制错误</li>
                <li><strong>测试同域iframe</strong>: 使用iframe-*策略测试本地iframe内的元素，应该正常工作</li>
                <li><strong>iframe配置示例</strong>:
                    <ul>
                        <li>iframe选择器: <code>#local-iframe</code></li>
                        <li>定位策略: <code>iframe-id</code></li>
                        <li>定位值: <code>submit-btn</code></li>
                    </ul>
                </li>
            </ol>
        </div>
    </div>

    <script>
        // 为主页面元素添加事件监听器
        document.getElementById('main-button').addEventListener('click', function() {
            alert('主页面按钮被点击！');
        });

        // 监听iframe加载事件
        const baiduIframe = document.getElementById('baidu-iframe');
        const localIframe = document.getElementById('local-iframe');

        baiduIframe.addEventListener('load', function() {
            console.log('百度iframe加载完成（可能被阻止）');
        });

        baiduIframe.addEventListener('error', function() {
            console.log('百度iframe加载失败（预期的跨域限制）');
        });

        localIframe.addEventListener('load', function() {
            console.log('本地iframe加载完成');
        });

        // 检测iframe访问权限
        setTimeout(function() {
            try {
                // 尝试访问百度iframe
                const baiduDoc = baiduIframe.contentDocument;
                if (baiduDoc) {
                    console.log('✅ 可以访问百度iframe内容');
                } else {
                    console.log('❌ 无法访问百度iframe内容（跨域限制）');
                }
            } catch (error) {
                console.log('❌ 百度iframe访问被阻止:', error.message);
            }

            try {
                // 尝试访问本地iframe
                const localDoc = localIframe.contentDocument;
                if (localDoc) {
                    console.log('✅ 可以访问本地iframe内容');
                } else {
                    console.log('❌ 无法访问本地iframe内容');
                }
            } catch (error) {
                console.log('❌ 本地iframe访问失败:', error.message);
            }
        }, 2000);
    </script>
</body>
</html>
