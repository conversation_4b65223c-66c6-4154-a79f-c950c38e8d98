<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iframe功能测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .iframe-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #007bff;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .iframe-section h2 {
            color: #007bff;
            margin-top: 0;
        }
        iframe {
            width: 100%;
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .main-content {
            margin: 20px 0;
            padding: 20px;
            background-color: #e9ecef;
            border-radius: 8px;
        }
        .test-button {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        .test-button:hover {
            background-color: #218838;
        }
        .test-input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 10px;
            font-size: 16px;
            width: 200px;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ iframe功能测试页面</h1>
        
        <div class="info">
            <h3>测试说明：</h3>
            <p>本页面包含多个iframe，用于测试自动化系统的iframe元素定位功能。</p>
            <p>您可以使用以下定位策略测试iframe内的元素：</p>
            <ul>
                <li><strong>iframe-css</strong>: 在iframe中使用CSS选择器</li>
                <li><strong>iframe-xpath</strong>: 在iframe中使用XPath</li>
                <li><strong>iframe-id</strong>: 在iframe中使用ID</li>
                <li><strong>iframe-text</strong>: 在iframe中使用文本内容</li>
                <li><strong>iframe-contains</strong>: 在iframe中使用包含文本</li>
            </ul>
        </div>

        <div class="main-content">
            <h2>主页面内容</h2>
            <button id="main-button" class="test-button">主页面按钮</button>
            <input id="main-input" class="test-input" placeholder="主页面输入框" />
            <p>这些元素在主页面中，不在iframe内。</p>
        </div>

        <div class="iframe-section">
            <h2>iframe 1 - 简单表单</h2>
            <p>iframe选择器: <code>#iframe1</code> 或 <code>iframe</code> (索引: 0)</p>
            <iframe id="iframe1" src="data:text/html;charset=utf-8,
                <!DOCTYPE html>
                <html>
                <head>
                    <style>
                        body { font-family: Arial, sans-serif; padding: 20px; background: #fff; }
                        .form-group { margin: 15px 0; }
                        label { display: block; margin-bottom: 5px; font-weight: bold; }
                        input, button { padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; }
                        button { background: #007bff; color: white; cursor: pointer; }
                        button:hover { background: #0056b3; }
                    </style>
                </head>
                <body>
                    <h3>iframe内的表单</h3>
                    <div class='form-group'>
                        <label for='username'>用户名:</label>
                        <input type='text' id='username' name='username' placeholder='请输入用户名' />
                    </div>
                    <div class='form-group'>
                        <label for='password'>密码:</label>
                        <input type='password' id='password' name='password' placeholder='请输入密码' />
                    </div>
                    <div class='form-group'>
                        <button id='submit-btn' type='button'>提交</button>
                        <button id='reset-btn' type='button'>重置</button>
                    </div>
                    <p>这是iframe1中的内容。</p>
                </body>
                </html>
            "></iframe>
        </div>

        <div class="iframe-section">
            <h2>iframe 2 - 复杂内容</h2>
            <p>iframe选择器: <code>#iframe2</code> 或 <code>iframe</code> (索引: 1)</p>
            <iframe id="iframe2" src="data:text/html;charset=utf-8,
                <!DOCTYPE html>
                <html>
                <head>
                    <style>
                        body { font-family: Arial, sans-serif; padding: 20px; background: #f8f9fa; }
                        .card { background: white; padding: 15px; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                        .btn { padding: 8px 16px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
                        .btn-primary { background: #007bff; color: white; }
                        .btn-success { background: #28a745; color: white; }
                        .btn-danger { background: #dc3545; color: white; }
                        .form-control { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; margin: 5px 0; }
                    </style>
                </head>
                <body>
                    <h3>iframe2 - 复杂内容</h3>
                    <div class='card'>
                        <h4>操作按钮</h4>
                        <button class='btn btn-primary' id='action1'>操作1</button>
                        <button class='btn btn-success' id='action2'>操作2</button>
                        <button class='btn btn-danger' id='action3'>删除</button>
                    </div>
                    <div class='card'>
                        <h4>输入区域</h4>
                        <input type='text' class='form-control' id='text-input' placeholder='文本输入' />
                        <input type='email' class='form-control' id='email-input' placeholder='邮箱输入' />
                        <textarea class='form-control' id='comment' placeholder='评论内容' rows='3'></textarea>
                    </div>
                    <div class='card'>
                        <h4>文本内容</h4>
                        <p>这是一段包含<span style='color: red;'>重要信息</span>的文本。</p>
                        <p>您可以通过文本内容定位这些元素。</p>
                        <div>确认操作</div>
                        <div>取消操作</div>
                    </div>
                </body>
                </html>
            "></iframe>
        </div>

        <div class="iframe-section">
            <h2>iframe 3 - 嵌套iframe</h2>
            <p>iframe选择器: <code>#iframe3</code> 或 <code>iframe</code> (索引: 2)</p>
            <iframe id="iframe3" src="data:text/html;charset=utf-8,
                <!DOCTYPE html>
                <html>
                <head>
                    <style>
                        body { font-family: Arial, sans-serif; padding: 20px; background: #e9ecef; }
                        iframe { width: 100%; height: 200px; border: 2px solid #6c757d; }
                    </style>
                </head>
                <body>
                    <h3>外层iframe</h3>
                    <p>这是外层iframe的内容。</p>
                    <button id='outer-btn'>外层按钮</button>
                    <h4>内嵌iframe:</h4>
                    <iframe src='data:text/html;charset=utf-8,
                        <!DOCTYPE html>
                        <html>
                        <body style=\"padding: 10px; background: #fff3cd;\">
                            <h5>内层iframe</h5>
                            <p>这是嵌套在iframe中的iframe内容。</p>
                            <button id=\"inner-btn\">内层按钮</button>
                            <input id=\"inner-input\" placeholder=\"内层输入框\" />
                        </body>
                        </html>
                    '></iframe>
                </body>
                </html>
            "></iframe>
        </div>

        <div class="info">
            <h3>测试建议：</h3>
            <ol>
                <li>首先测试主页面的元素（不使用iframe策略）</li>
                <li>然后测试iframe1中的简单表单元素</li>
                <li>测试iframe2中的复杂内容和不同类型的元素</li>
                <li>尝试不同的iframe选择器和索引配置</li>
                <li>验证iframe加载超时设置</li>
            </ol>
        </div>
    </div>

    <script>
        // 为主页面元素添加事件监听器
        document.getElementById('main-button').addEventListener('click', function() {
            alert('主页面按钮被点击！');
        });

        // 等待iframe加载完成后为iframe内元素添加事件监听器
        window.addEventListener('load', function() {
            setTimeout(function() {
                try {
                    // iframe1 事件
                    const iframe1 = document.getElementById('iframe1');
                    if (iframe1 && iframe1.contentDocument) {
                        const submitBtn = iframe1.contentDocument.getElementById('submit-btn');
                        if (submitBtn) {
                            submitBtn.addEventListener('click', function() {
                                alert('iframe1 提交按钮被点击！');
                            });
                        }
                    }

                    // iframe2 事件
                    const iframe2 = document.getElementById('iframe2');
                    if (iframe2 && iframe2.contentDocument) {
                        const action1 = iframe2.contentDocument.getElementById('action1');
                        if (action1) {
                            action1.addEventListener('click', function() {
                                alert('iframe2 操作1按钮被点击！');
                            });
                        }
                    }
                } catch (error) {
                    console.log('设置iframe事件监听器时出错:', error);
                }
            }, 1000);
        });
    </script>
</body>
</html>
