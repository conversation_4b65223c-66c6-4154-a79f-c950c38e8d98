{"name": "百度iframe演示模板", "description": "演示iframe功能和跨域限制的测试模板", "version": "1.0.0", "steps": [{"id": "step1", "name": "点击主页面按钮", "type": "click", "action": "click", "locator": {"strategy": "id", "value": "main-button"}, "waitAfterClick": 1000, "description": "点击主页面的按钮（非iframe内）"}, {"id": "step2", "name": "在主页面输入框输入内容", "type": "input", "action": "input", "locator": {"strategy": "id", "value": "main-input"}, "inputText": "主页面测试内容", "clearFirst": true, "description": "在主页面输入框中输入内容"}, {"id": "step3", "name": "尝试访问百度iframe（预期失败）", "type": "click", "action": "click", "locator": {"strategy": "iframe-id", "value": "su", "iframeSelector": "#baidu-iframe", "iframeIndex": 0, "waitForIframe": 3000}, "waitAfterClick": 1000, "description": "尝试点击百度搜索按钮（预期因跨域限制而失败）"}, {"id": "step4", "name": "在本地iframe中输入测试内容", "type": "input", "action": "input", "locator": {"strategy": "iframe-id", "value": "test-input", "iframeSelector": "#local-iframe", "iframeIndex": 0, "waitForIframe": 5000}, "inputText": "iframe内测试文本", "clearFirst": true, "description": "在本地iframe的测试输入框中输入内容"}, {"id": "step5", "name": "在本地iframe中输入搜索内容", "type": "input", "action": "input", "locator": {"strategy": "iframe-id", "value": "search-input", "iframeSelector": "#local-iframe", "iframeIndex": 0, "waitForIframe": 5000}, "inputText": "iframe搜索测试", "clearFirst": true, "description": "在本地iframe的搜索输入框中输入内容"}, {"id": "step6", "name": "点击本地iframe中的提交按钮", "type": "click", "action": "click", "locator": {"strategy": "iframe-id", "value": "submit-btn", "iframeSelector": "#local-iframe", "iframeIndex": 0, "waitForIframe": 5000}, "waitAfterClick": 2000, "description": "点击本地iframe中的提交按钮"}, {"id": "step7", "name": "使用CSS选择器点击搜索按钮", "type": "click", "action": "click", "locator": {"strategy": "iframe-css", "value": "#search-btn", "iframeSelector": "#local-iframe", "iframeIndex": 0, "waitForIframe": 5000}, "waitAfterClick": 2000, "description": "使用CSS选择器点击本地iframe中的搜索按钮"}, {"id": "step8", "name": "使用文本内容定位清空按钮", "type": "click", "action": "click", "locator": {"strategy": "iframe-text", "value": "清空按钮", "iframeSelector": "#local-iframe", "iframeIndex": 0, "waitForIframe": 5000}, "waitAfterClick": 1000, "description": "通过文本内容定位并点击本地iframe中的清空按钮"}, {"id": "step9", "name": "使用包含文本定位按钮", "type": "click", "action": "click", "locator": {"strategy": "iframe-contains", "value": "提交", "iframeSelector": "#local-iframe", "iframeIndex": 0, "waitForIframe": 5000}, "waitAfterClick": 1000, "description": "通过包含文本定位并点击本地iframe中包含'提交'的按钮"}, {"id": "step10", "name": "使用XPath定位输入框", "type": "input", "action": "input", "locator": {"strategy": "iframe-xpath", "value": "//input[@type='text' and @id='test-input']", "iframeSelector": "#local-iframe", "iframeIndex": 0, "waitForIframe": 5000}, "inputText": "XPath定位测试", "clearFirst": true, "description": "使用XPath定位本地iframe中的输入框并输入内容"}], "settings": {"execution": {"stepDelay": 1500, "retryCount": 2, "timeout": 15000, "continueOnError": true}, "iframe": {"defaultWaitTime": 5000, "maxRetries": 2, "enableCrossDomainWarning": true, "skipCrossDomainErrors": true}}, "metadata": {"testUrl": "test-pages/baidu-iframe-demo.html", "tags": ["iframe", "跨域", "演示", "百度"], "difficulty": "中级", "estimatedTime": "3分钟", "notes": ["步骤3预期会失败，这是正常的跨域限制", "步骤4-10应该正常工作，因为是同域iframe", "可以观察到跨域和同域iframe的不同行为"]}}