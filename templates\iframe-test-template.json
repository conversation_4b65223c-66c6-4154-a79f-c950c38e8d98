{"name": "iframe功能测试模板", "description": "演示如何在iframe中定位和操作元素的测试模板", "version": "1.0.0", "steps": [{"id": "step1", "name": "点击主页面按钮", "type": "click", "action": "click", "locator": {"strategy": "id", "value": "main-button"}, "waitAfterClick": 1000, "description": "点击主页面的按钮（非iframe内）"}, {"id": "step2", "name": "在主页面输入框输入内容", "type": "input", "action": "input", "locator": {"strategy": "id", "value": "main-input"}, "inputText": "主页面输入测试", "clearFirst": true, "description": "在主页面输入框中输入内容"}, {"id": "step3", "name": "在iframe1中输入用户名", "type": "input", "action": "input", "locator": {"strategy": "iframe-id", "value": "username", "iframeSelector": "#iframe1", "iframeIndex": 0, "waitForIframe": 5000}, "inputText": "testuser", "clearFirst": true, "description": "在iframe1的用户名输入框中输入内容"}, {"id": "step4", "name": "在iframe1中输入密码", "type": "input", "action": "input", "locator": {"strategy": "iframe-id", "value": "password", "iframeSelector": "#iframe1", "iframeIndex": 0, "waitForIframe": 5000}, "inputText": "password123", "clearFirst": true, "description": "在iframe1的密码输入框中输入内容"}, {"id": "step5", "name": "点击iframe1中的提交按钮", "type": "click", "action": "click", "locator": {"strategy": "iframe-id", "value": "submit-btn", "iframeSelector": "#iframe1", "iframeIndex": 0, "waitForIframe": 5000}, "waitAfterClick": 1000, "description": "点击iframe1中的提交按钮"}, {"id": "step6", "name": "使用CSS选择器点击iframe2中的按钮", "type": "click", "action": "click", "locator": {"strategy": "iframe-css", "value": ".btn-primary", "iframeSelector": "#iframe2", "iframeIndex": 1, "waitForIframe": 5000}, "waitAfterClick": 1000, "description": "使用CSS选择器点击iframe2中的主要按钮"}, {"id": "step7", "name": "在iframe2中输入文本", "type": "input", "action": "input", "locator": {"strategy": "iframe-id", "value": "text-input", "iframeSelector": "#iframe2", "iframeIndex": 1, "waitForIframe": 5000}, "inputText": "iframe2测试文本", "clearFirst": true, "description": "在iframe2的文本输入框中输入内容"}, {"id": "step8", "name": "使用文本内容定位iframe2中的元素", "type": "click", "action": "click", "locator": {"strategy": "iframe-text", "value": "确认操作", "iframeSelector": "#iframe2", "iframeIndex": 1, "waitForIframe": 5000}, "waitAfterClick": 1000, "description": "通过文本内容定位并点击iframe2中的确认操作元素"}, {"id": "step9", "name": "使用XPath定位iframe2中的删除按钮", "type": "click", "action": "click", "locator": {"strategy": "iframe-xpath", "value": "//button[contains(@class, 'btn-danger')]", "iframeSelector": "#iframe2", "iframeIndex": 1, "waitForIframe": 5000}, "waitAfterClick": 1000, "description": "使用XPath定位并点击iframe2中的删除按钮"}, {"id": "step10", "name": "使用包含文本定位iframe2中的元素", "type": "click", "action": "click", "locator": {"strategy": "iframe-contains", "value": "重要信息", "iframeSelector": "#iframe2", "iframeIndex": 1, "waitForIframe": 5000}, "waitAfterClick": 1000, "description": "通过包含文本定位并点击iframe2中包含'重要信息'的元素"}, {"id": "step11", "name": "点击iframe3外层按钮", "type": "click", "action": "click", "locator": {"strategy": "iframe-id", "value": "outer-btn", "iframeSelector": "#iframe3", "iframeIndex": 2, "waitForIframe": 5000}, "waitAfterClick": 1000, "description": "点击iframe3中的外层按钮"}], "settings": {"execution": {"stepDelay": 1500, "retryCount": 3, "timeout": 30000}, "iframe": {"defaultWaitTime": 5000, "maxRetries": 3, "enableCrossDomainWarning": true}}, "metadata": {"testUrl": "test-pages/iframe-test.html", "tags": ["iframe", "测试", "定位器", "跨框架"], "difficulty": "中级", "estimatedTime": "5分钟"}}